import express from 'express';
import cors from 'cors';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import { loopRouter } from './routes/loop.js';
import { secretsRouter } from './routes/secrets.js';
import { logsRouter } from './routes/logs.js';
import { queueRouter } from './routes/queue.js';
import { onboardingRouter } from './routes/onboarding.js';
import { versionRouter } from './routes/version.js';
import { billingRouter } from './routes/billing.js';
import { repositoriesRouter } from './routes/repositories.js';
import { aiRouter } from './routes/ai.js';
import { autoPRRouter } from './routes/autoPR.js';
import { costGuardRouter } from './routes/costGuard.js';
import { supabaseClient } from './services/supabase.js';
import { secretsService } from './services/secretsService.js';
import { loggingService } from './services/loggingService.js';
import {
  helmetConfig,
  nonceMiddleware,
  generalRateLimit,
  strictRateLimit,
  secretStrippingMiddleware,
  inputValidationMiddleware,
  securityLoggingMiddleware
} from './middleware/security.js';

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Security middleware (must be first)
app.use(nonceMiddleware); // Generate nonce for each request
app.use(helmetConfig); // Apply helmet with nonce
app.use(securityLoggingMiddleware);
app.use(generalRateLimit);

// CORS configuration
const corsOptions = {
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Body parsing with size limits
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input validation and secret stripping
app.use(inputValidationMiddleware);
app.use(secretStrippingMiddleware);

// Request logging middleware
app.use((req, res, next) => {
  const startTime = Date.now();
  const requestId = req.headers['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  res.on('finish', async () => {
    const responseTime = Date.now() - startTime;

    try {
      await loggingService.log({
        level: res.statusCode >= 400 ? 'error' : 'info',
        message: `${req.method} ${req.path} - ${res.statusCode}`,
        service: 'api',
        metadata: {
          method: req.method,
          path: req.path,
          status_code: res.statusCode,
          response_time: responseTime,
          user_agent: req.get('User-Agent'),
          ip: req.ip,
          request_id: requestId
        },
        request_id: requestId
      });
    } catch (error) {
      console.error('Failed to log request:', error);
    }
  });

  next();
});

// Routes with additional rate limiting for sensitive endpoints
app.use('/api/secrets', strictRateLimit); // Apply strict rate limiting to secrets endpoints
app.use('/api', loopRouter);
app.use('/api', secretsRouter);
app.use('/api', logsRouter);
app.use('/api', queueRouter);
app.use('/api', onboardingRouter);
app.use('/api', versionRouter);
app.use('/api', billingRouter);
app.use('/api', repositoriesRouter);
app.use('/api', aiRouter);
app.use('/api', autoPRRouter);
app.use('/api', costGuardRouter);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// WebSocket for real-time streaming
wss.on('connection', (ws) => {
  console.log('Client connected to WebSocket');
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log('Received WebSocket message:', data);
      
      // Echo back for now - extend for real-time loop updates
      ws.send(JSON.stringify({
        type: 'echo',
        data,
        timestamp: new Date().toISOString()
      }));
    } catch (error) {
      console.error('WebSocket message error:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format',
        timestamp: new Date().toISOString()
      }));
    }
  });
  
  ws.on('close', () => {
    console.log('Client disconnected from WebSocket');
  });
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, async () => {
  console.log(`🚀 Metamorphic Reactor API running on port ${PORT}`);
  console.log(`📡 WebSocket server ready for real-time streaming`);

  // Initialize secrets from environment variables
  try {
    await secretsService.initializeFromEnv();
    console.log('🔐 Secrets service initialized');
  } catch (error) {
    console.error('Failed to initialize secrets service:', error);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
